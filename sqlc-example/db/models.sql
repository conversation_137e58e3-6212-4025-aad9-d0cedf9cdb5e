CREATE TABLE classes (
    id   INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON>R(100) NOT NULL
);

CREATE TABLE students (
    id        INT AUTO_INCREMENT PRIMARY KEY,
    name      VARCHAR(100) NOT NULL,
    class_id  INT,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL
);

CREATE TABLE assignments (
    id          INT AUTO_INCREMENT PRIMARY KEY,
    title       VARCHAR(100) NOT NULL,
    student_id  INT,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);
