// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: query.sql

package db

import (
	"context"
	"database/sql"
)

const createAssignment = `-- name: CreateAssignment :exec
INSERT INTO assignments (title, student_id) VALUES (?, ?)
`

type CreateAssignmentParams struct {
	Title     string
	StudentID sql.NullInt32
}

// Assignments
func (q *Queries) CreateAssignment(ctx context.Context, arg CreateAssignmentParams) error {
	_, err := q.db.ExecContext(ctx, createAssignment, arg.Title, arg.StudentID)
	return err
}

const createClass = `-- name: CreateClass :exec
INSERT INTO classes (name) VALUES (?)
`

// Classes
func (q *Queries) CreateClass(ctx context.Context, name string) error {
	_, err := q.db.ExecContext(ctx, createClass, name)
	return err
}

const createStudent = `-- name: CreateStudent :exec
INSERT INTO students (name, class_id) VALUES (?, ?)
`

type CreateStudentParams struct {
	Name    string
	ClassID sql.NullInt32
}

// Students
func (q *Queries) CreateStudent(ctx context.Context, arg CreateStudentParams) error {
	_, err := q.db.ExecContext(ctx, createStudent, arg.Name, arg.ClassID)
	return err
}

const getStudentWithClass = `-- name: GetStudentWithClass :one
SELECT s.id, s.name, c.id AS class_id, c.name AS class_name
FROM students s
LEFT JOIN classes c ON s.class_id = c.id
WHERE s.id = ?
`

type GetStudentWithClassRow struct {
	ID        int32
	Name      string
	ClassID   sql.NullInt32
	ClassName sql.NullString
}

func (q *Queries) GetStudentWithClass(ctx context.Context, id int32) (GetStudentWithClassRow, error) {
	row := q.db.QueryRowContext(ctx, getStudentWithClass, id)
	var i GetStudentWithClassRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ClassID,
		&i.ClassName,
	)
	return i, err
}

const listAssignmentsByStudent = `-- name: ListAssignmentsByStudent :many
SELECT id, title, student_id FROM assignments WHERE student_id = ?
`

func (q *Queries) ListAssignmentsByStudent(ctx context.Context, studentID sql.NullInt32) ([]Assignment, error) {
	rows, err := q.db.QueryContext(ctx, listAssignmentsByStudent, studentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Assignment
	for rows.Next() {
		var i Assignment
		if err := rows.Scan(&i.ID, &i.Title, &i.StudentID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listClasses = `-- name: ListClasses :many
SELECT id, name FROM classes
`

func (q *Queries) ListClasses(ctx context.Context) ([]Class, error) {
	rows, err := q.db.QueryContext(ctx, listClasses)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Class
	for rows.Next() {
		var i Class
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listStudents = `-- name: ListStudents :many
SELECT id, name, class_id FROM students
`

func (q *Queries) ListStudents(ctx context.Context) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, listStudents)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(&i.ID, &i.Name, &i.ClassID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
